#!/usr/bin/env python3
"""
最简单的测试版本
只使用基本的Python库，不依赖复杂的包
"""

import json
import random
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading
import webbrowser
import time

class SimpleWeatherAgent:
    """简化的天气查询智能体"""
    
    def __init__(self):
        self.cities = ["北京", "上海", "广州", "深圳", "杭州", "成都", "西安", "武汉", "重庆", "天津"]
        self.weather_conditions = ["晴朗", "多云", "阴天", "小雨", "中雨", "雪"]
    
    def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            # 检查是否是天气查询
            weather_keywords = ["天气", "温度", "下雨", "晴天", "阴天", "多云", "雪", "风", "气温"]
            is_weather_query = any(keyword in user_input for keyword in weather_keywords)
            
            if is_weather_query:
                # 提取城市名称
                city = None
                for c in self.cities:
                    if c in user_input:
                        city = c
                        break
                
                if city:
                    # 生成天气数据
                    temperature = random.randint(-5, 35)
                    condition = random.choice(self.weather_conditions)
                    humidity = random.randint(30, 90)
                    wind_speed = random.randint(0, 15)
                    
                    response = f"根据最新天气信息，{city}今天{condition}，温度{temperature}°C，湿度{humidity}%，风速{wind_speed}km/h。\n\n"
                    
                    # 添加建议
                    if temperature < 0:
                        response += "天气较冷，建议穿厚外套，注意保暖。"
                    elif temperature < 15:
                        response += "天气偏凉，建议穿长袖衣物。"
                    elif temperature > 30:
                        response += "天气炎热，建议穿轻薄衣物，注意防晒。"
                    else:
                        response += "天气适宜，穿着舒适即可。"
                    
                    if "雨" in condition:
                        response += "有降雨，出行请携带雨具。"
                    
                    return response
                else:
                    return "请告诉我您想查询哪个城市的天气？我支持查询以下城市：" + "、".join(self.cities)
            else:
                return "您好！我是航天质量风险管理智能体的天气查询助手。我可以为您查询天气信息。请告诉我您想查询哪个城市的天气？"
        
        except Exception as e:
            return f"处理消息时发生错误: {str(e)}"

# 全局智能体实例
agent = SimpleWeatherAgent()
chat_history = []

class RequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航天质量风险管理智能体</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa; 
            margin: 0; 
            padding: 20px;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
        }
        .title { 
            color: #2c3e50; 
            text-align: center; 
            margin-bottom: 30px;
            font-weight: 600;
        }
        .chat-container { 
            max-height: 500px; 
            overflow-y: auto; 
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message { 
            margin-bottom: 15px; 
            padding: 12px 16px; 
            border-radius: 18px; 
            max-width: 80%;
        }
        .user-message { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            margin-left: auto;
            text-align: right;
        }
        .agent-message { 
            background: #f1f3f4; 
            color: #333;
            margin-right: auto;
        }
        .input-form {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-control {
            width: 100%;
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            font-size: 16px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 航天质量风险管理智能体</h1>
        
        <div id="chat-container" class="chat-container">
            <!-- 聊天消息将在这里显示 -->
        </div>
        
        <div class="input-form">
            <input type="text" id="messageInput" class="form-control" 
                   placeholder="请输入您的问题（例如：北京今天天气怎么样？）" />
            <button onclick="sendMessage()" class="btn">发送</button>
        </div>
    </div>

    <script>
        function addMessage(content, isUser) {
            const chatContainer = document.getElementById('chat-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + (isUser ? 'user-message' : 'agent-message');
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage(message, true);
            
            // 清空输入框
            input.value = '';
            
            // 发送到服务器
            fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'message=' + encodeURIComponent(message)
            })
            .then(response => response.text())
            .then(data => {
                addMessage(data, false);
            })
            .catch(error => {
                addMessage('发送消息时发生错误: ' + error, false);
            });
        }

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
            """
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/chat':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            params = parse_qs(post_data)
            
            message = params.get('message', [''])[0]
            
            if message:
                response = agent.process_message(message)
                
                self.send_response(200)
                self.send_header('Content-type', 'text/plain; charset=utf-8')
                self.end_headers()
                self.wfile.write(response.encode('utf-8'))
            else:
                self.send_response(400)
                self.end_headers()
        else:
            self.send_response(404)
            self.end_headers()

def run_server():
    """运行服务器"""
    port = 8000
    server = HTTPServer(('127.0.0.1', port), RequestHandler)
    print(f"服务器启动成功！")
    print(f"访问地址: http://127.0.0.1:{port}")
    
    # 自动打开浏览器
    def open_browser():
        time.sleep(1)
        webbrowser.open(f'http://127.0.0.1:{port}')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        server.shutdown()

if __name__ == "__main__":
    print("正在启动航天质量风险管理智能体测试版...")
    run_server()
