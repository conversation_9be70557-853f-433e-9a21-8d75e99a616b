#!/usr/bin/env python3
"""
简化的天气查询智能体
用于测试，不依赖复杂的LangGraph和外部API
"""

import json
import random
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class SimpleAgentConfig:
    """简化智能体配置"""
    model_name: str = "test-model"

class SimpleWeatherAgent:
    """简化的天气查询智能体"""
    
    def __init__(self, config: SimpleAgentConfig):
        self.config = config
        self.cities = ["北京", "上海", "广州", "深圳", "杭州", "成都", "西安", "武汉", "重庆", "天津"]
        self.weather_conditions = ["晴朗", "多云", "阴天", "小雨", "中雨", "雪"]
    
    def _extract_city(self, user_input: str) -> str:
        """从用户输入中提取城市名称"""
        for city in self.cities:
            if city in user_input:
                return city
        return None
    
    def _is_weather_query(self, user_input: str) -> bool:
        """判断是否是天气查询"""
        weather_keywords = ["天气", "温度", "下雨", "晴天", "阴天", "多云", "雪", "风", "气温"]
        return any(keyword in user_input for keyword in weather_keywords)
    
    def _generate_weather_data(self, city: str) -> Dict[str, Any]:
        """生成模拟天气数据"""
        temperature = random.randint(-5, 35)
        condition = random.choice(self.weather_conditions)
        humidity = random.randint(30, 90)
        wind_speed = random.randint(0, 15)
        
        return {
            "city": city,
            "condition": condition,
            "temperature": temperature,
            "humidity": humidity,
            "wind_speed": wind_speed,
            "description": f"今天{condition}，温度{temperature}°C，湿度{humidity}%，风速{wind_speed}km/h"
        }
    
    def _generate_weather_advice(self, weather_data: Dict[str, Any]) -> str:
        """根据天气数据生成建议"""
        temperature = weather_data["temperature"]
        condition = weather_data["condition"]
        
        advice = []
        
        if temperature < 0:
            advice.append("天气较冷，建议穿厚外套，注意保暖。")
        elif temperature < 15:
            advice.append("天气偏凉，建议穿长袖衣物。")
        elif temperature > 30:
            advice.append("天气炎热，建议穿轻薄衣物，注意防晒。")
        else:
            advice.append("天气适宜，穿着舒适即可。")
        
        if "雨" in condition:
            advice.append("有降雨，出行请携带雨具。")
        elif condition == "雪":
            advice.append("有降雪，路面可能湿滑，出行注意安全。")
        elif condition == "晴朗":
            advice.append("天气晴朗，适合户外活动。")
        
        return " ".join(advice)
    
    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            # 检查是否是天气查询
            if self._is_weather_query(user_input):
                # 提取城市名称
                city = self._extract_city(user_input)
                
                if city:
                    # 生成天气数据
                    weather_data = self._generate_weather_data(city)
                    
                    # 构建响应
                    response = f"根据最新天气信息，{city}{weather_data['description']}。\n\n"
                    
                    # 添加建议
                    advice = self._generate_weather_advice(weather_data)
                    response += advice
                    
                    return response
                else:
                    return "请告诉我您想查询哪个城市的天气？我支持查询以下城市：" + "、".join(self.cities)
            else:
                return "您好！我是航天质量风险管理智能体的天气查询助手。我可以为您查询天气信息。请告诉我您想查询哪个城市的天气？"
        
        except Exception as e:
            return f"处理消息时发生错误: {str(e)}"
    
    async def close(self):
        """关闭资源"""
        pass

def create_simple_weather_agent(config: SimpleAgentConfig) -> SimpleWeatherAgent:
    """创建简化天气查询智能体实例"""
    return SimpleWeatherAgent(config)
