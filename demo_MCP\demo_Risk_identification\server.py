# 一个 mcp 服务端例子
# 注意：add函数一定要写注释，@mcp.tool()会根据add函数的注释自动添加函数的JSON_Schema说明格式，决定了大模型是否认识并选择调用该函数
# MCP客户端和服务器通信有2种方式：
#   1）标准I/O：更适用于本地编写或把服务端代码pull到本地访问的场景
#   2）Http SSE：更适用于远程访问MCP服务端的场景
from mcp.server.fastmcp import FastMCP

# 创建 MCP 服务
mcp = FastMCP('demo_Risk_identification')

@mcp.tool()
def Risk_identification(md_file_in:str) -> str:
  """
  将MD文件中的表格转换为文本
  """
  # 解析表格
  headers, data_rows = parse_html_table(md_file_in)
  # 格式化输出
  md_file_out = format_output(headers, data_rows)
    
    # # 打印结果
    # print(output)
    
    # # 保存到文件
    # with open('output.txt', 'w', encoding='utf-8') as f:
    #     f.write(output)
    # print("结果已保存到 output.txt 文件中")


  return md_file_out






if __name__ == "__main__":
  # 以标准 I/O 方式运行 MCP 服务器
  # MCP服务器3种通信方式：（参考：https://juejin.cn/post/7505970081802944563）
  #   1. Stdio：
  #      工作原理：将MCP Server作为MCP Client的子进程，双方通过约定的管道进行通信。是目前最常用的方式。
  #      缺点：性能与本地算力息息相关，所以只适合做一些简单的场景，不能作为企业级分布式应用。
  #   2. Http SSE：
  #      工作原理：基于HTTP协议的事件传输机制。客户端通过HTTP请求发送消息到服务端，服务端通过专门的/sse端点向客户端推送消息。
  #      缺点：这种通信方式看似解决了Stdio不能分机部署的弊端，但其设计存在以下问题，这些问题导致MCP难以扩展到企业级：
  #           1）不支持断线重连/恢复：SSE连接断开所有会话状态丢失，客户端必须重新建立连接并初始化整个会话。
  #           2）服务器需维护长连接：服务器必须为每个客户端维护一个长时间的SSE连接，大量并发用户会导致资源消耗剧增，当服务器重启或扩容时所有连接会中断影响用户体验和系统可靠性。
  #           3）服务器消息只能通过SSE传递：即使是简单的请求-响应交互，服务器也必须通过SSE通道返回消息，这就需要服务器一直保持SSE连接，造成不必要的复杂性和开销。
  #           4）基础设施兼容性限制：目前很多Web基础设施如CDN、负载均衡器、API网关等对长时间SSE连接支持性不够，企业防火墙有可能强制关闭超时SSE连接，造成连接不可能。
  #   3. Streamable HTTP：
  #      工作原理：服务端流式响应客户端。
  #      优点：解决SSE的4个问题，成为企业级MCP应用。
  mcp.run(transport='stdio')

