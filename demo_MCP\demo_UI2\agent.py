#!/usr/bin/env python3
"""
使用LangGraph框架构建的天气查询智能体
集成MCP协议调用天气查询工具
"""

import os
import json
import asyncio
import subprocess
import random
from typing import Dict, List, Any, Optional, TypedDict
from dataclasses import dataclass

# 注释掉复杂依赖，使用简化版本
# from langchain_openai import ChatOpenAI
# from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
# from langgraph.graph import StateGraph, END

@dataclass
class WeatherAgentConfig:
    """天气智能体配置"""
    openai_api_key: str
    openai_base_url: str = "https://api.openai.com/v1"
    model_name: str = "gpt-3.5-turbo"
    temperature: float = 0.7

class AgentState(TypedDict):
    """智能体状态"""
    messages: List[Any]
    user_input: str
    weather_data: Optional[str]
    response: str
    analysis: Optional[Dict]

class WeatherAgent:
    """天气查询智能体"""

    def __init__(self, config: WeatherAgentConfig):
        self.config = config
        # 为了测试，暂时不初始化LLM
        self.llm = None
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流"""
        workflow = StateGraph(AgentState)

        # 添加节点
        workflow.add_node("analyze_input", self._analyze_input)
        workflow.add_node("call_weather_tool", self._call_weather_tool)
        workflow.add_node("generate_response", self._generate_response)

        # 设置入口点
        workflow.set_entry_point("analyze_input")

        # 添加边
        workflow.add_conditional_edges(
            "analyze_input",
            self._should_call_weather_tool,
            {
                "call_weather": "call_weather_tool",
                "direct_response": "generate_response"
            }
        )

        workflow.add_edge("call_weather_tool", "generate_response")
        workflow.add_edge("generate_response", END)

        return workflow.compile()

    async def _analyze_input(self, state: AgentState) -> AgentState:
        """分析用户输入"""
        user_input = state["user_input"]

        # 简单的关键词匹配来判断是否需要查询天气
        weather_keywords = ["天气", "温度", "下雨", "晴天", "阴天", "多云", "雪", "风"]
        cities = ["北京", "上海", "广州", "深圳", "杭州", "成都", "西安", "武汉", "重庆", "天津"]

        needs_weather = any(keyword in user_input for keyword in weather_keywords)
        city = None

        for c in cities:
            if c in user_input:
                city = c
                break

        state["analysis"] = {
            "needs_weather": needs_weather,
            "city": city,
            "intent": "天气查询" if needs_weather else "一般对话"
        }

        return state

    def _should_call_weather_tool(self, state: AgentState) -> str:
        """判断是否需要调用天气工具"""
        analysis = state.get("analysis", {})
        if analysis.get("needs_weather", False) and analysis.get("city"):
            return "call_weather"
        return "direct_response"

    async def _call_weather_tool(self, state: AgentState) -> AgentState:
        """调用天气工具（简化版本，直接生成模拟数据）"""
        analysis = state.get("analysis", {})
        city = analysis.get("city")

        if not city:
            state["weather_data"] = None
            return state

        # 生成模拟天气数据
        conditions = ["晴朗", "多云", "阴天", "小雨", "中雨"]
        temperature = random.randint(-5, 35)
        condition = random.choice(conditions)
        humidity = random.randint(30, 90)
        wind_speed = random.randint(0, 15)

        weather_info = {
            "city": city,
            "condition": condition,
            "temperature": temperature,
            "humidity": humidity,
            "wind_speed": wind_speed,
            "description": f"今天{condition}，温度{temperature}°C，湿度{humidity}%，风速{wind_speed}km/h"
        }

        state["weather_data"] = json.dumps(weather_info, ensure_ascii=False)
        return state

    async def _generate_response(self, state: AgentState) -> AgentState:
        """生成最终响应"""
        user_input = state["user_input"]
        weather_data = state.get("weather_data")

        if weather_data:
            # 基于天气数据生成响应
            weather_info = json.loads(weather_data)
            city = weather_info["city"]
            condition = weather_info["condition"]
            temperature = weather_info["temperature"]
            humidity = weather_info["humidity"]
            wind_speed = weather_info["wind_speed"]

            response = f"根据最新天气信息，{city}今天{condition}，温度{temperature}°C，湿度{humidity}%，风速{wind_speed}km/h。\n\n"

            # 添加建议
            if temperature < 0:
                response += "天气较冷，建议穿厚外套，注意保暖。"
            elif temperature < 15:
                response += "天气偏凉，建议穿长袖衣物。"
            elif temperature > 30:
                response += "天气炎热，建议穿轻薄衣物，注意防晒。"
            else:
                response += "天气适宜，穿着舒适即可。"

            if "雨" in condition:
                response += "有降雨，出行请携带雨具。"

            state["response"] = response
        else:
            # 直接回答
            state["response"] = "您好！我是航天质量风险管理智能体的天气查询助手。我可以为您查询以下城市的天气信息：北京、上海、广州、深圳、杭州、成都、西安、武汉、重庆、天津。请告诉我您想查询哪个城市的天气？"

        return state

    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            initial_state = AgentState(
                messages=[],
                user_input=user_input,
                weather_data=None,
                response="",
                analysis=None
            )

            # 运行LangGraph工作流
            final_state = await self.graph.ainvoke(initial_state)
            return final_state["response"]

        except Exception as e:
            return f"处理消息时发生错误: {str(e)}"

    async def close(self):
        """关闭资源"""
        pass

def create_weather_agent(config: WeatherAgentConfig) -> WeatherAgent:
    """创建天气查询智能体实例"""
    return WeatherAgent(config)
