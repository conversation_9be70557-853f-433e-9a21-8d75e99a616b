# 天气查询智能体

基于LangGraph框架和MCP协议的天气查询智能体，使用FastHTML提供简洁的Web界面。

## 功能特性

- 🤖 使用LangGraph框架构建智能体工作流
- 🌤️ 通过MCP协议调用天气查询工具
- 🎨 简洁的FastHTML Web界面
- 🏙️ 支持中国主要城市天气查询

## 支持的城市

北京、上海、广州、深圳、杭州、成都、西安、武汉、重庆、天津

## 快速开始

### 1. 环境要求

- Python 3.11+
- conda环境：langgraph-env

### 2. 配置环境

```bash
# 激活conda环境
conda activate langgraph-env

# 配置API密钥
# 编辑.env文件，设置您的OpenAI API密钥
```

### 3. 运行应用

```bash
python run.py
```

或直接运行：

```bash
python app.py
```

### 4. 访问应用

打开浏览器访问：http://127.0.0.1:8000

## 项目结构

```
demo_UI2/
├── agent.py           # LangGraph智能体实现
├── weather_server.py  # MCP天气查询服务器
├── app.py            # FastHTML Web应用
├── run.py            # 启动脚本
├── .env              # 环境配置
├── pyproject.toml    # 项目配置
└── README.md         # 项目说明
```

## 技术架构

1. **LangGraph**: 构建智能体工作流，包含输入分析、工具调用、响应生成等节点
2. **MCP协议**: 通过Model Context Protocol调用天气查询工具
3. **FastHTML**: 提供简洁的Web用户界面
4. **异步处理**: 支持异步消息处理和工具调用

## 使用示例

- "北京今天天气怎么样？"
- "上海的天气如何？"
- "深圳现在下雨吗？"
- "成都的温度是多少？"
