#!/usr/bin/env python3
"""
FastHTML UI应用
为航天质量风险管理智能体提供Web界面
"""

import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any

from fasthtml.common import *
from dotenv import load_dotenv

#from simple_agent import SimpleQualityRiskAgent, SimpleAgentConfig
from agent import QualityRiskAgent, QualityRiskAgentConfig

# 加载环境变量
load_dotenv()

# 应用配置
app_config = {
    "host": os.getenv("HOST", "127.0.0.1"),
    "port": int(os.getenv("PORT", 8000)),
    "debug": os.getenv("DEBUG", "True").lower() == "true"
}

# 创建FastHTML应用
app, rt = fast_app()

# 全局变量存储聊天历史和智能体实例
chat_history: List[Dict[str, Any]] = []
quality_agent: QualityRiskAgent = None

def init_agent():
    """初始化智能体"""
    global quality_agent

    try:
        config = QualityRiskAgentConfig(
            openai_api_key=os.getenv("OPENAI_API_KEY", ""),
            openai_base_url=os.getenv("OPENAI_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
            model_name="qwen-max",
            temperature=0.2
        )

        if not config.openai_api_key:
            print("警告: 未设置OPENAI_API_KEY，智能体功能可能受限")

        quality_agent = QualityRiskAgent(config)
        print("智能体初始化成功")

    except Exception as e:
        print(f"智能体初始化失败: {e}")

def create_message_div(message: Dict[str, Any]) -> Div:
    """创建消息显示组件"""
    is_user = message["type"] == "user"
    css_class = "user-message" if is_user else "agent-message"
    
    return Div(
        Div(message["content"], cls="message-content"),
        Div(message["timestamp"], cls="timestamp"),
        cls=f"message {css_class}"
    )

def create_chat_interface() -> Div:
    """创建聊天界面"""
    chat_messages = [create_message_div(msg) for msg in chat_history]
    
    return Div(
        # 聊天历史显示区域
        Div(
            *chat_messages,
            id="chat-messages",
            cls="chat-container mb-3"
        ),
        
        # 输入区域
        Form(
            Div(
                Input(
                    type="text",
                    name="message",
                    placeholder="请输入您的问题（例如：航天器发射前需要考虑哪些质量风险？）",
                    cls="form-control",
                    required=True,
                    autocomplete="off"
                ),
                cls="col-md-10"
            ),
            Div(
                Button(
                    "发送",
                    type="submit",
                    cls="btn btn-primary w-100"
                ),
                cls="col-md-2"
            ),
            cls="row g-2",
            hx_post="/chat",
            hx_target="#chat-messages",
            hx_swap="innerHTML",
            hx_indicator="#loading"
        ),
     
    )

@rt("/")
def homepage():
    """主页"""
    """主页"""
    return Html(
        Head(
            Title("航天质量风险管理智能体"),
            Meta(charset="utf-8"),
            Meta(name="viewport", content="width=device-width, initial-scale=1"),
            Link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"),
            Script(src="https://unpkg.com/htmx.org@1.9.10"),
            Style("""
                .chat-container { max-height: 500px; overflow-y: auto; }
                .message { margin-bottom: 10px; padding: 10px; border-radius: 10px; }
                .user-message { background-color: #007bff; color: white; margin-left: 20%; }
                .ai-message { background-color: #f8f9fa; margin-right: 20%; }
                .loading { display: none; }
            """)
        ),
        Body(
            Div(
                H1("🚀 航天质量风险管理智能体", cls="text-center mb-4"),
                P("", cls="text-center text-muted mb-4"),
                
                # 聊天容器
                Div(id="chat-container", cls="chat-container border p-3 mb-3"),
                
                # 输入表单
                Form(
                    Div(
                        Input(
                            type="text", 
                            name="message", 
                            placeholder="请输入您的问题", 
                            cls="form-control",
                            required=True
                        ),
                        cls="input-group mb-2"
                    ),
                    Button("发送", type="submit", cls="btn btn-primary"),
                    Div("正在处理中...", cls="loading text-muted mt-2"),
                    hx_post="/chat",
                    hx_target="#chat-container",
                    hx_swap="beforeend",
                    hx_indicator=".loading"
                ),
                
                # 示例查询
                Div(
                    H5("示例查询："),
                    Ul(
                        Li("火箭研制阶段的风险分析"),
                        Li("卫星在轨运行风险评估")
                    ),
                    cls="mt-4"
                ),
                
                cls="container mt-4"
            ),
            Script(src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js")
        )
    )


@rt("/chat", methods=["POST"])
async def chat_endpoint(message: str):
    """处理聊天请求"""
    global chat_history, quality_agent

    try:
        # 添加用户消息到历史
        user_message = {
            "type": "user",
            "content": message,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(user_message)

        # 获取智能体响应
        if quality_agent:
            agent_response = await quality_agent.process_message(message)
        else:
            agent_response = "抱歉，智能体尚未初始化。请检查配置并重启应用。"

        # 添加智能体响应到历史
        agent_message = {
            "type": "agent",
            "content": agent_response,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(agent_message)

        # 返回更新后的聊天消息
        chat_messages = [create_message_div(msg) for msg in chat_history]
        return Div(*chat_messages)

    except Exception as e:
        error_message = {
            "type": "agent",
            "content": f"处理请求时发生错误: {str(e)}",
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(error_message)

        chat_messages = [create_message_div(msg) for msg in chat_history]
        return Div(*chat_messages)

@rt("/health")
def health_check():
    """健康检查端点"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    print("正在启动应用...")

    # 初始化智能体
    print("初始化智能体...")
    init_agent()

    # 添加欢迎消息
    welcome_message = {
        "type": "agent",
        "content": "您好！我是航天质量风险管理智能体。我可以帮助您分析航天器全生命周期的质量风险，提供专业的风险评估和管理建议。请告诉我您想了解哪方面的质量风险管理问题？",
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    chat_history.append(welcome_message)

    print(f"启动FastHTML应用...")
    port = 8000
    print(f"访问地址: http://{app_config['host']}:{port}")

    # 启动应用
    serve(host=app_config["host"], port=port)
