#!/usr/bin/env python3
"""
启动脚本
用于启动航天质量风险管理智能体应用
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_conda_env():
    """检查是否在正确的conda环境中"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'langgraph-env':
        print("⚠️  警告: 当前不在 'langgraph-env' conda环境中")
        print(f"当前环境: {conda_env or '未知'}")
        print("\n请先激活正确的conda环境:")
        print("conda activate langgraph-env")
        return False
    return True

def check_dependencies():
    """检查必要的依赖是否已安装"""
    # 定义包名和对应的导入名称映射
    required_packages = {
        'langgraph': 'langgraph',
        'langchain': 'langchain',
        'langchain-openai': 'langchain_openai',
        'fasthtml': 'fasthtml',  # python-fasthtml 包导入为 fasthtml
        'mcp': 'mcp',
        'httpx': 'httpx',
        'uvicorn': 'uvicorn',
        'python-dotenv': 'dotenv',
        'requests': 'requests'
    }

    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请安装缺少的依赖:")
        print("pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_env_file():
    """检查环境配置文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 未找到 .env 文件，请根据 .env.example 创建配置文件")
            print("\n复制示例配置文件:")
            if platform.system() == "Windows":
                print("copy .env.example .env")
            else:
                print("cp .env.example .env")
            print("\n然后编辑 .env 文件，设置您的API密钥")
        else:
            print("❌ 未找到环境配置文件")
        return False
    
    # 检查关键配置
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        openai_key = os.getenv("OPENAI_API_KEY")
        if not openai_key or openai_key == "your_openai_api_key_here":
            print("⚠️  警告: 未设置有效的 OPENAI_API_KEY")
            print("智能体功能可能受限，请在 .env 文件中设置正确的API密钥")
    except Exception as e:
        print(f"❌ 读取环境配置失败: {e}")
        return False
    
    return True

def start_application():
    """启动应用"""
    try:
        print("🚀 启动航天质量风险管理智能体...")
        print("=" * 50)
        
        # 运行FastHTML应用
        subprocess.run([sys.executable, "app.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 应用启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔍 检查运行环境...")
    
    # 检查conda环境
    if not check_conda_env():
        sys.exit(1)
    
    # 检查依赖
    print("📦 检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    
    # 检查环境配置
    print("⚙️  检查环境配置...")
    env_ok = check_env_file()
    
    print("✅ 环境检查完成")
    print()
    
    if not env_ok:
        response = input("环境配置有问题，是否继续启动？(y/N): ")
        if response.lower() != 'y':
            print("请先完成环境配置后再启动应用")
            sys.exit(1)
    
    # 启动应用
    start_application()

if __name__ == "__main__":
    main()
