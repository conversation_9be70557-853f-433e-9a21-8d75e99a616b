#!/usr/bin/env python3
"""
启动脚本
用于启动天气查询智能体应用
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("错误: 需要Python 3.11或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ Conda环境: {conda_env}")
    else:
        print("警告: 未检测到conda环境")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("安装项目依赖...")
    
    try:
        # 使用pip安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-e", "."
        ], check=True, capture_output=True, text=True)
        
        print("✓ 依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"错误: 依赖安装失败")
        print(f"错误信息: {e.stderr}")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("创建.env文件...")
        with open(env_file, "w", encoding="utf-8") as f:
            f.write("""# OpenAI API配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 应用配置
HOST=127.0.0.1
PORT=8000
DEBUG=True
""")
        print("✓ 已创建.env文件，请配置您的API密钥")
        return False
    
    # 检查API密钥是否配置
    with open(env_file, "r", encoding="utf-8") as f:
        content = f.read()
        if "your_api_key_here" in content:
            print("警告: 请在.env文件中配置您的API密钥")
            return False
    
    print("✓ 环境配置文件检查通过")
    return True

def start_application():
    """启动应用"""
    print("启动天气查询智能体应用...")
    
    try:
        # 启动FastHTML应用
        subprocess.run([sys.executable, "app.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"错误: 应用启动失败")
        print(f"错误信息: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("天气查询智能体启动器")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        return
    
    # 检查环境变量文件
    env_configured = check_env_file()
    
    # 安装依赖
    if not install_dependencies():
        return
    
    if not env_configured:
        print("\n请先配置.env文件中的API密钥，然后重新运行此脚本")
        return
    
    # 启动应用
    start_application()

if __name__ == "__main__":
    main()
