#!/usr/bin/env python3
"""
FastHTML UI应用
为天气查询智能体提供简洁的Web界面
只显示"航天质量风险管理智能体"标题和问答框
"""

import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any

from websockets import serve

from fasthtml.common import *
from dotenv import load_dotenv

from simple_agent import SimpleWeatherAgent, SimpleAgentConfig

# 加载环境变量
load_dotenv()

# 创建FastHTML应用
app, rt = fast_app()

# 全局变量存储聊天历史和智能体实例
chat_history: List[Dict[str, Any]] = []
weather_agent: SimpleWeatherAgent = None

def init_agent():
    """初始化智能体"""
    global weather_agent

    try:
        config = SimpleAgentConfig()
        weather_agent = SimpleWeatherAgent(config)
        print("简化天气智能体初始化成功")

    except Exception as e:
        print(f"智能体初始化失败: {e}")

def create_message_div(message: Dict[str, Any]) -> Div:
    """创建消息显示组件"""
    is_user = message["type"] == "user"
    css_class = "user-message" if is_user else "agent-message"
    
    return Div(
        Div(message["content"], cls="message-content"),
        Div(message["timestamp"], cls="timestamp"),
        cls=f"message {css_class}"
    )

@rt("/")
def homepage():
    """主页 - 简洁界面，只显示标题和问答框"""
    return Html(
        Head(
            Title("航天质量风险管理智能体"),
            Meta(charset="utf-8"),
            Meta(name="viewport", content="width=device-width, initial-scale=1"),
            Link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"),
            Script(src="https://unpkg.com/htmx.org@1.9.10"),
            Style("""
                body { 
                    background-color: #f8f9fa; 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                .main-container { 
                    max-width: 800px; 
                    margin: 0 auto; 
                    padding: 20px;
                }
                .title { 
                    color: #2c3e50; 
                    text-align: center; 
                    margin-bottom: 30px;
                    font-weight: 600;
                }
                .chat-container { 
                    max-height: 500px; 
                    overflow-y: auto; 
                    background: white;
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .message { 
                    margin-bottom: 15px; 
                    padding: 12px 16px; 
                    border-radius: 18px; 
                    max-width: 80%;
                }
                .user-message { 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white; 
                    margin-left: auto;
                    text-align: right;
                }
                .agent-message { 
                    background: #f1f3f4; 
                    color: #333;
                    margin-right: auto;
                }
                .message-content {
                    margin-bottom: 5px;
                    line-height: 1.4;
                }
                .timestamp { 
                    font-size: 0.8em; 
                    opacity: 0.7; 
                }
                .input-form {
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .form-control {
                    border-radius: 25px;
                    border: 2px solid #e9ecef;
                    padding: 12px 20px;
                    font-size: 16px;
                }
                .form-control:focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                }
                .btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border: none;
                    border-radius: 25px;
                    padding: 12px 30px;
                    font-weight: 600;
                }
                .btn-primary:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                }
                .loading { 
                    display: none; 
                    text-align: center;
                    color: #6c757d;
                    margin-top: 10px;
                }
            """)
        ),
        Body(
            Div(
                # 标题
                H1("🚀 航天质量风险管理智能体", cls="title"),
                
                # 聊天容器
                Div(id="chat-container", cls="chat-container"),
                
                # 输入表单
                Div(
                    Form(
                        Div(
                            Input(
                                type="text", 
                                name="message", 
                                placeholder="请输入您的问题（例如：北京今天天气怎么样？）", 
                                cls="form-control",
                                required=True,
                                autocomplete="off"
                            ),
                            cls="mb-3"
                        ),
                        Button("发送", type="submit", cls="btn btn-primary w-100"),
                        Div("正在处理中...", cls="loading"),
                        hx_post="/chat",
                        hx_target="#chat-container",
                        hx_swap="beforeend",
                        hx_indicator=".loading"
                    ),
                    cls="input-form"
                ),
                
                cls="main-container"
            ),
            Script(src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"),
            Script("""
                // 自动滚动到底部
                function scrollToBottom() {
                    const container = document.getElementById('chat-container');
                    container.scrollTop = container.scrollHeight;
                }
                
                // 监听HTMX事件
                document.body.addEventListener('htmx:afterSwap', function(evt) {
                    scrollToBottom();
                    // 清空输入框
                    const input = document.querySelector('input[name="message"]');
                    if (input) input.value = '';
                });
            """)
        )
    )

@rt("/chat", methods=["POST"])
async def chat_endpoint(message: str):
    """处理聊天请求"""
    global chat_history, weather_agent

    try:
        # 添加用户消息到历史
        user_message = {
            "type": "user",
            "content": message,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(user_message)

        # 获取智能体响应
        if weather_agent:
            agent_response = await weather_agent.process_message(message)
        else:
            agent_response = "抱歉，智能体尚未初始化。请检查配置并重启应用。"

        # 添加智能体响应到历史
        agent_message = {
            "type": "agent",
            "content": agent_response,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(agent_message)

        # 返回新消息
        return Div(
            create_message_div(user_message),
            create_message_div(agent_message)
        )

    except Exception as e:
        error_message = {
            "type": "agent",
            "content": f"处理请求时发生错误: {str(e)}",
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        chat_history.append(error_message)

        return create_message_div(error_message)

@rt("/health")
def health_check():
    """健康检查端点"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    print("正在启动天气查询智能体应用...")

    # 初始化智能体
    print("初始化智能体...")
    init_agent()

    print(f"启动FastHTML应用...")
    port = 8000
    print(f"访问地址: http://127.0.0.1:{port}")

    # 启动应用
    serve(host="127.0.0.1", port=port)
